# 选择模式功能说明

## 概述

本次更新为生态厂商、自有能力方和自有联系人选择组件添加了单选和多选两种模式支持，用户可以在同一时间使用其中一种模式。

## 功能特性

### 1. 双模式支持
- **单选模式 (single)**: 传统的单选功能，一次只能选择一个选项
- **多选模式 (multiple)**: 新增的多选功能，可以同时选择多个选项

### 2. 选择限制
- **生态厂商**: 最多可选择 5 个
- **总选择数量**: 生态厂商 + 自有能力方 + 自有联系人 总共最多 7 个
- **超出限制提示**: 当达到限制时会显示相应的提示信息

### 3. 全局模式控制
- 通过全局选择模式控制所有组件的选择行为
- 切换模式时会自动清空当前选择状态
- 在基础信息区域提供模式切换按钮

## 组件更新

### 1. PartnerList.vue (生态厂商列表)
- 添加 `selectionMode` prop 控制选择模式
- 支持 `selectedValue` (单选) 和 `selectedValues` (多选) 两种数据绑定
- 添加 `componentType` prop 用于区分组件类型
- 新增 `limit-exceeded` 事件处理限制超出情况

### 2. OwnCapabilitySelector (自有能力方选择)
- 同样支持单选/多选模式切换
- 添加相应的 props 和事件处理

### 3. OwnContactSelector (自有联系人选择)
- 支持单选/多选模式
- 保持原有的 `valueField` 配置灵活性

### 4. EcoPartnerSelector (生态厂商选择主组件)
- 作为容器组件，传递选择模式到子组件
- 处理限制超出事件并向上传递

## 数据结构更新

### 模块表单新增字段
```javascript
{
  // 原有字段...
  selectedValues: [], // 多选模式下的选中值数组
  selectedEcoValues: [], // 生态厂商选中值
  selectedOwnValues: [], // 自有能力方选中值
  selectedContactValues: [], // 自有联系人选中值
  selectionMode: 'single', // 选择模式: single(单选) 或 multiple(多选)
}
```

### 全局配置
```javascript
{
  globalSelectionMode: 'single', // 全局选择模式控制
}
```

## 使用方法

### 1. 基本使用
```vue
<EcoPartnerSelector
  :company-data="moduleItem.editDataCompany"
  :selection-mode="moduleItem.selectionMode || data.globalSelectionMode"
  :selected-value="moduleItem.selectId || ''"
  :selected-values="moduleItem.selectedValues || []"
  :reject-company-id-list="moduleItem.rejectCompanyIdlist || []"
  @selection-change="handleSelectionChange"
  @limit-exceeded="handleLimitExceeded"
/>
```

### 2. 模式切换
```javascript
// 切换全局选择模式
const toggleSelectionMode = () => {
  data.globalSelectionMode = data.globalSelectionMode === 'single' ? 'multiple' : 'single';
  // 清空所有模块的选择状态
  data.formData.moduleForm.forEach(module => {
    module.selectedValues = [];
    module.selectId = "";
    module.selectIdOwn = "";
    module.selectPhone = "";
    module.selectCompanyList = {};
  });
};
```

### 3. 处理选择事件
```javascript
const onCheckChange = (e, item, index) => {
  const moduleForm = data.formData.moduleForm[index];
  const selectionMode = moduleForm.selectionMode || data.globalSelectionMode;
  
  if (selectionMode === 'single') {
    // 单选逻辑
    const { value } = e.target;
    moduleForm.selectId = value;
    // ...
  } else {
    // 多选逻辑
    const { checked } = e.target;
    const value = item.contactPhone;
    
    if (checked) {
      moduleForm.selectedValues.push(value);
    } else {
      const index = moduleForm.selectedValues.indexOf(value);
      moduleForm.selectedValues.splice(index, 1);
    }
  }
};
```

## 限制规则

### 多选模式限制
1. **生态厂商限制**: 最多选择 5 个生态厂商
2. **总数限制**: 所有类型选择总数不超过 7 个
3. **提示信息**: 
   - 生态厂商超限: "生态厂商最多支持选5个"
   - 总数超限: "最多可以选择7个"

### 单选模式
- 保持原有的单选行为
- 一次只能选择一个选项
- 选择新项目时自动取消之前的选择

## 兼容性

- 完全向后兼容现有的单选功能
- 默认模式为单选模式
- 现有代码无需修改即可正常工作
- 新功能通过 props 控制，不影响现有逻辑

## 测试

可以使用 `SelectionModeDemo.vue` 组件进行功能测试：
- 模式切换测试
- 限制验证测试
- 选择状态管理测试

## 注意事项

1. 切换模式时会清空当前选择状态
2. 多选模式下需要注意数组数据的处理
3. 限制检查在组件内部进行，确保用户体验
4. 事件处理需要区分单选和多选的不同逻辑
