# 组件拆分说明

## 概述

本次重构将原有的生态厂商、自有能力方和自由联系人的选择功能拆分为三个独立的组件，提高了代码的可维护性和复用性。

## 组件结构

```bash
components/
├── EcoPartnerSelector/          # 生态厂商选择组件
│   ├── index.vue               # 主组件
│   ├── SearchForm.vue          # 搜索表单子组件
│   └── PartnerList.vue         # 厂商列表子组件
├── OwnCapabilitySelector/       # 自有能力方选择组件
│   └── index.vue               # 主组件
├── OwnContactSelector/         # 自由联系人选择组件
│   └── index.vue               # 主组件
├── ComponentTest.vue           # 组件测试页面
└── README.md                   # 说明文档
```

## 组件功能

### 1. EcoPartnerSelector (生态厂商选择组件)

**功能特点：**

- 支持按名称和评分范围搜索
- 分页显示
- 认证状态验证
- 联系人选择
- 新增生态厂商功能

**Props：**

- `companyData`: 公司数据数组
- `ecologyType`: 生态类型
- `selectedValue`: 当前选中的值
- `rejectCompanyIdList`: 被拒绝的公司ID列表
- `showAddButton`: 是否显示新增按钮
- `contactSelectMode`: 联系人选择模式 ('userId' | 'contactName')

**Events：**

- `selection-change`: 选择变化事件
- `contact-change`: 联系人变化事件
- `company-detail`: 公司详情事件
- `add-partner`: 新增生态厂商事件

### 2. OwnCapabilitySelector (自有能力方选择组件)

**功能特点：**

- 简单的列表展示
- 基本的选择功能
- 禁用状态处理

**Props：**

- `ownPersonData`: 自有能力方数据数组
- `ecologyType`: 生态类型
- `selectedValue`: 当前选中的值
- `rejectCompanyIdList`: 被拒绝的公司ID列表

**Events：**

- `selection-change`: 选择变化事件

### 3. OwnContactSelector (自由联系人选择组件)

**功能特点：**

- 联系人信息展示
- 基本的选择功能
- 可配置选择值字段

**Props：**

- `contactData`: 联系人数据数组
- `selectedValue`: 当前选中的值
- `rejectCompanyIdList`: 被拒绝的联系人ID列表
- `valueField`: 选择值的字段名

**Events：**

- `selection-change`: 选择变化事件

## 使用示例

### 生态厂商选择组件

```bash
<EcoPartnerSelector
  :company-data="moduleItem.editDataCompany"
  :ecology-type="moduleItem.ecologyType"
  :selected-value="moduleItem.selectId"
  :reject-company-id-list="moduleItem.rejectCompanyIdlist"
  :contact-select-mode="'userId'"
  @selection-change="(e, company) => onCheckChange(e, company, moduleIndex)"
  @contact-change="(value, company) => selectUserCom(value, company)"
  @company-detail="toCompanyDetail"
  @add-partner="() => addCooperate(moduleItem.editDataCompany[0], moduleIndex)"
/>
```

### 自有能力方选择组件

```bash
<OwnCapabilitySelector
  :own-person-data="moduleItem.editDataCompany"
  :ecology-type="moduleItem.ecologyType"
  :selected-value="moduleItem.selectIdOwn"
  :reject-company-id-list="moduleItem.rejectCompanyIdlist"
  @selection-change="(e, person) => onCheckChange(e, person, moduleIndex)"
/>
```

### 自由联系人选择组件

```bash
<OwnContactSelector
  :contact-data="moduleItem.ecopartnerList"
  :selected-value="moduleItem.selectIdOwn"
  :reject-company-id-list="moduleItem.rejectCompanyIdlist"
  :value-field="'belong'"
  @selection-change="(e, contact) => onCheckChange(e, contact, moduleIndex)"
/>
```

## 性能优化

1. **搜索缓存**: 生态厂商组件实现了搜索结果缓存，避免重复计算
2. **按需渲染**: 只有在有数据时才渲染组件
3. **事件优化**: 使用事件委托减少事件监听器数量
4. **分页处理**: 大数据量时使用分页显示，提高渲染性能

## 兼容性

- 保持与原有API的兼容性
- 事件参数格式与原有保持一致
- 样式继承原有设计规范

## 测试

可以通过访问 `ComponentTest.vue` 页面来测试各个组件的功能。

## 维护建议

1. 每个组件职责单一，便于独立维护
2. 组件间通过props和events通信，耦合度低
3. 可以根据需要独立优化每个组件
4. 便于编写单元测试

## 集成完成状态

### ✅ 已完成的集成工作

1. **完全替换原有表格**: 所有生态厂商、自有能力方、自由联系人的表格都已替换为新组件
2. **优化Props传递**: 添加了默认值和空值保护，确保组件稳定性
3. **事件处理完善**: 所有事件处理函数都正确连接到新组件
4. **条件渲染优化**: 确保在不同状态下组件正确显示
5. **错误边界处理**: 添加了数据验证和错误处理逻辑
6. **性能优化**: 移除了不再需要的方法和数据，减少内存占用

### 🔧 集成优化点

1. **数据安全**: 所有props都添加了默认值（`|| ''` 或 `|| []`）
2. **组件通信**: 事件参数格式与原有保持完全一致
3. **样式继承**: 新组件继承了原有的样式设计
4. **功能完整**: 搜索、分页、选择等所有功能都正常工作

### 📋 测试验证

- 创建了 `ComponentTest.vue` 测试页面
- 提供了 `IntegrationTest.js` 测试工具
- 包含了完整的模拟数据和测试用例
- 支持性能测试和数据验证

## 后续扩展

1. 可以为每个组件添加更多的配置选项
2. 可以抽取更多的公共逻辑到mixins或composables
3. 可以添加更多的自定义事件支持
4. 可以考虑添加虚拟滚动支持大数据量场景
5. 可以添加单元测试覆盖
6. 可以考虑添加TypeScript支持
