<template>
  <el-table v-if="companyData && companyData.length > 0" :data="companyData" :empty-text="'暂无数据'"
    :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }" border
    style="width: 100%; margin-top: 20px; margin-bottom: 20px">
    <el-table-column v-if="!ecologyType" prop="company" label="生态厂商" />
    <el-table-column v-if="ecologyType && ecologyType.includes('2')" prop="company" label="生态厂商">
      <template #header>
        <div class="table-header">
          <span>生态厂商</span>
          <a-button v-if="showAddButton" class="custom_btn active_btn add_btn" @click="handleAddPartner">
            新增生态厂商
          </a-button>
        </div>
      </template>
      <template #default="scope">
        <div>
          <!-- 搜索表单 -->
          <SearchForm v-model="searchForm" @search="handleSearch" @reset="handleReset" />

          <!-- 生态厂商列表 -->
          <PartnerList :company-list="getFilteredCompanyList(scope.row.company)"
            :selection-mode="selectionMode"
            :selected-value="selectedValue"
            :selected-values="selectedValues"
            :reject-company-id-list="rejectCompanyIdList" :contact-select-mode="contactSelectMode"
            :component-type="'eco'"
            @selection-change="handleSelectionChange" @contact-change="handleContactChange"
            @company-detail="handleCompanyDetail" @limit-exceeded="handleLimitExceeded" />

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <a-pagination v-model:pageSize="pageSize" v-model:current="currentPage"
              :show-total="total => `共 ${total} 条`" :pageSizeOptions="pageSizeOptions" show-quick-jumper
              show-size-changer :total="getFilteredTotal(scope.row.company)" @change="handlePageChange"
              @showSizeChange="handleSizeChange" />
          </div>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import { defineComponent, reactive, ref } from 'vue'
import SearchForm from './SearchForm.vue'
import PartnerList from './PartnerList.vue'

export default defineComponent({
  name: 'EcoPartnerSelector',
  components: {
    SearchForm,
    PartnerList
  },
  props: {
    // 公司数据
    companyData: {
      type: Array,
      default: () => []
    },
    // 生态类型
    ecologyType: {
      type: String,
      default: ''
    },
    // 选择模式 (single: 单选, multiple: 多选)
    selectionMode: {
      type: String,
      default: 'single',
      validator: (value) => ['single', 'multiple'].includes(value)
    },
    // 单选模式下的选中值
    selectedValue: {
      type: String,
      default: ''
    },
    // 多选模式下的选中值数组
    selectedValues: {
      type: Array,
      default: () => []
    },
    // 被拒绝的公司ID列表
    rejectCompanyIdList: {
      type: Array,
      default: () => []
    },
    // 是否显示新增按钮
    showAddButton: {
      type: Boolean,
      default: true
    },
    // 联系人选择模式
    contactSelectMode: {
      type: String,
      default: 'userId'
    }
  },
  emits: ['selection-change', 'contact-change', 'company-detail', 'add-partner', 'limit-exceeded'],
  setup(props, { emit }) {
    // 搜索表单数据
    const searchForm = reactive({
      ecopartnerName: '',
      minScore: '',
      maxScore: ''
    })

    // 搜索状态
    const searchState = reactive({
      isApplied: false,
      cachedResults: null
    })

    // 分页数据
    const currentPage = ref(1)
    const pageSize = ref(5)
    const pageSizeOptions = reactive(['5', '10', '20', '30', '50'])

    // 执行搜索逻辑
    const executeSearch = (list) => {
      const { ecopartnerName, minScore, maxScore } = searchForm
      let filteredList = [...list]

      // 按企业或联系人名称过滤（支持模糊搜索）
      if (ecopartnerName && ecopartnerName.trim()) {
        filteredList = filteredList.filter(company =>
        (company.ecopartnerName &&
          company.ecopartnerName.includes(ecopartnerName.trim()) || (company.contactName && company.contactName.includes(ecopartnerName.trim())))
        );
      }

      // 按评分范围过滤
      if (minScore !== '' || maxScore !== '') {
        filteredList = filteredList.filter(company => {
          const score = company.totalScore || company.introScore || 0
          const min = minScore === '' ? -Infinity : Number(minScore)
          const max = maxScore === '' ? Infinity : Number(maxScore)
          return score >= min && score <= max
        })
      }
      return filteredList
    }

    // 获取过滤后的公司列表
    const getFilteredCompanyList = (allList) => {
      if (!allList || !Array.isArray(allList)) return []

      // 如果没有应用搜索，直接返回分页结果
      if (!searchState.isApplied) {
        const start = (currentPage.value - 1) * pageSize.value
        const end = start + pageSize.value
        return allList.slice(start, end)
      }
      // 检查缓存
      if (searchState.cachedResults) {
        const start = (currentPage.value - 1) * pageSize.value
        const end = start + pageSize.value
        return searchState.cachedResults.slice(start, end)
      }

      // 执行搜索并缓存
      const filteredResults = executeSearch(allList)
      searchState.cachedResults = filteredResults

      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return filteredResults.slice(start, end)
    }

    // 获取过滤后的总数
    const getFilteredTotal = (allList) => {
      if (!allList || !Array.isArray(allList)) return 0

      if (!searchState.isApplied) {
        return allList.length
      }

      if (searchState.cachedResults) {
        return searchState.cachedResults.length
      }

      const filteredResults = executeSearch(allList)
      searchState.cachedResults = filteredResults
      return filteredResults.length
    }

    // 处理搜索
    const handleSearch = (form) => {
      searchForm.ecopartnerName = form.ecopartnerName || ''
      searchForm.minScore = form.minScore || ''
      searchForm.maxScore = form.maxScore || ''
      searchState.isApplied = !(!form.ecopartnerName && !form.minScore && !form.maxScore)
      currentPage.value = 1
      searchState.cachedResults = null
    }

    // 处理重置
    const handleReset = () => {
      searchForm.ecopartnerName = ''
      searchForm.minScore = ''
      searchForm.maxScore = ''
      searchState.isApplied = false
      searchState.cachedResults = null
      currentPage.value = 1
    }

    // 处理分页变化
    const handlePageChange = (page) => {
      currentPage.value = page
    }

    const handleSizeChange = (size, pageSizeArg) => {
      const actualSize = pageSizeArg || size
      pageSize.value = actualSize
      currentPage.value = 1
    }

    // 处理选择变化
    const handleSelectionChange = (e, company) => {
      emit('selection-change', e, company)
    }

    // 处理联系人变化
    const handleContactChange = (value, company) => {
      emit('contact-change', value, company)
    }

    // 处理公司详情
    const handleCompanyDetail = (company) => {
      emit('company-detail', company)
    }

    // 处理新增生态厂商
    const handleAddPartner = () => {
      emit('add-partner')
    }

    // 处理限制超出
    const handleLimitExceeded = (message) => {
      emit('limit-exceeded', message)
    }

    return {
      searchForm,
      currentPage,
      pageSize,
      pageSizeOptions,
      getFilteredCompanyList,
      getFilteredTotal,
      handleSearch,
      handleReset,
      handlePageChange,
      handleSizeChange,
      handleSelectionChange,
      handleContactChange,
      handleCompanyDetail,
      handleAddPartner,
      handleLimitExceeded
    }
  }
})
</script>

<style lang="scss" scoped>
.table-header {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.custom_btn {
  color: #FFFFFF;
  border-radius: 4px 4px 4px 4px;
  border: none;
}


.active_btn {
  background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
}

.cancel_btn {
  background: rgba(12, 112, 235, 0.08);
  color: #0C70EB;
}

.custom_btn:hover,
.custom_btn:focus {
  opacity: 0.6;
}

.add_btn {
  font-size: 12px;
  padding: 2px 8px;
  position: absolute;
  right: 8px;
}
</style>
