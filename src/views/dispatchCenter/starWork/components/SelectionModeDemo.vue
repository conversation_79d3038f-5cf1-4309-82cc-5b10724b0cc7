<template>
  <div class="selection-mode-demo">
    <h3>选择模式演示</h3>
    
    <!-- 模式切换 -->
    <div class="mode-controls">
      <a-button 
        type="primary" 
        @click="toggleMode"
        style="margin-bottom: 20px;"
      >
        {{ selectionMode === 'single' ? '切换到多选模式' : '切换到单选模式' }}
      </a-button>
      <span style="margin-left: 10px;">
        当前模式: {{ selectionMode === 'single' ? '单选' : '多选' }}
        <span v-if="selectionMode === 'multiple'" style="color: #ff6b35;">
          (生态厂商最多5个，总共最多7个)
        </span>
      </span>
    </div>

    <!-- 生态厂商选择 -->
    <div class="section">
      <h4>生态厂商选择</h4>
      <EcoPartnerSelector
        :company-data="mockEcoData"
        :selection-mode="selectionMode"
        :selected-value="singleSelectedValue"
        :selected-values="multipleSelectedValues"
        :reject-company-id-list="[]"
        :contact-select-mode="'userId'"
        @selection-change="handleEcoSelectionChange"
        @limit-exceeded="handleLimitExceeded"
      />
    </div>

    <!-- 自有能力方选择 -->
    <div class="section">
      <h4>自有能力方选择</h4>
      <OwnCapabilitySelector
        :own-person-data="mockOwnData"
        :selection-mode="selectionMode"
        :selected-value="singleSelectedValue"
        :selected-values="multipleSelectedValues"
        :reject-company-id-list="[]"
        @selection-change="handleOwnSelectionChange"
        @limit-exceeded="handleLimitExceeded"
      />
    </div>

    <!-- 选择结果显示 -->
    <div class="results">
      <h4>选择结果</h4>
      <p v-if="selectionMode === 'single'">
        单选值: {{ singleSelectedValue || '未选择' }}
      </p>
      <p v-else>
        多选值: {{ multipleSelectedValues.length > 0 ? multipleSelectedValues.join(', ') : '未选择' }}
        (已选择 {{ multipleSelectedValues.length }} 个)
      </p>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import EcoPartnerSelector from './EcoPartnerSelector/index.vue'
import OwnCapabilitySelector from './OwnCapabilitySelector/index.vue'

export default defineComponent({
  name: 'SelectionModeDemo',
  components: {
    EcoPartnerSelector,
    OwnCapabilitySelector
  },
  setup() {
    const selectionMode = ref('single')
    const singleSelectedValue = ref('')
    const multipleSelectedValues = ref([])

    // 模拟数据
    const mockEcoData = reactive([{
      name: '测试模块',
      company: [
        {
          ecopartnerName: '生态厂商A',
          contactPhone: '13800138001',
          contactName: '联系人A',
          userId: 'user001',
          auth: 1,
          sync: 1,
          approve: 1,
          totalScore: 85,
          contactList: [
            { contactName: '联系人A', userId: 'user001', contactPhone: '13800138001', approve: 1 }
          ]
        },
        {
          ecopartnerName: '生态厂商B',
          contactPhone: '13800138002',
          contactName: '联系人B',
          userId: 'user002',
          auth: 1,
          sync: 1,
          approve: 1,
          totalScore: 90,
          contactList: [
            { contactName: '联系人B', userId: 'user002', contactPhone: '13800138002', approve: 1 }
          ]
        },
        {
          ecopartnerName: '生态厂商C',
          contactPhone: '13800138003',
          contactName: '联系人C',
          userId: 'user003',
          auth: 1,
          sync: 1,
          approve: 1,
          totalScore: 78,
          contactList: [
            { contactName: '联系人C', userId: 'user003', contactPhone: '13800138003', approve: 1 }
          ]
        }
      ]
    }])

    const mockOwnData = reactive([{
      name: '测试模块',
      ownPerson: [
        {
          belong: '自有能力方A',
          contactPhone: '13900139001',
          contactName: '自有联系人A',
          userId: 'own001'
        },
        {
          belong: '自有能力方B',
          contactPhone: '13900139002',
          contactName: '自有联系人B',
          userId: 'own002'
        }
      ]
    }])

    // 切换模式
    const toggleMode = () => {
      selectionMode.value = selectionMode.value === 'single' ? 'multiple' : 'single'
      // 清空选择
      singleSelectedValue.value = ''
      multipleSelectedValues.value = []
    }

    // 处理生态厂商选择
    const handleEcoSelectionChange = (e, company) => {
      if (selectionMode.value === 'single') {
        singleSelectedValue.value = e.target.value
      } else {
        const { checked } = e.target
        const value = company.contactPhone
        
        if (checked) {
          if (!multipleSelectedValues.value.includes(value)) {
            multipleSelectedValues.value.push(value)
          }
        } else {
          const index = multipleSelectedValues.value.indexOf(value)
          if (index > -1) {
            multipleSelectedValues.value.splice(index, 1)
          }
        }
      }
    }

    // 处理自有能力方选择
    const handleOwnSelectionChange = (e, person) => {
      if (selectionMode.value === 'single') {
        singleSelectedValue.value = e.target.value
      } else {
        const { checked } = e.target
        const value = person.contactPhone
        
        if (checked) {
          if (!multipleSelectedValues.value.includes(value)) {
            multipleSelectedValues.value.push(value)
          }
        } else {
          const index = multipleSelectedValues.value.indexOf(value)
          if (index > -1) {
            multipleSelectedValues.value.splice(index, 1)
          }
        }
      }
    }

    // 处理限制超出
    const handleLimitExceeded = (msg) => {
      message.warning(msg)
    }

    return {
      selectionMode,
      singleSelectedValue,
      multipleSelectedValues,
      mockEcoData,
      mockOwnData,
      toggleMode,
      handleEcoSelectionChange,
      handleOwnSelectionChange,
      handleLimitExceeded
    }
  }
})
</script>

<style lang="scss" scoped>
.selection-mode-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.mode-controls {
  margin-bottom: 30px;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 6px;
}

.section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  
  h4 {
    margin-bottom: 15px;
    color: #333;
  }
}

.results {
  padding: 15px;
  background: #f9f9f9;
  border-radius: 6px;
  
  h4 {
    margin-bottom: 10px;
    color: #333;
  }
  
  p {
    margin: 5px 0;
    font-family: monospace;
  }
}
</style>
